import os
from pydantic_settings import BaseSettings


class Settings(BaseSettings):
    # Registry配置
    registry_library_url: str = "registry.datasecchk.net/library"
    registry_artifacts_url: str = "registry.datasecchk.net/artifacts"
    registry_username: str = ""
    registry_password: str = ""
    
    # 应用配置
    temp_dir: str = "./temp"
    default_base_image: str = "alpine:latest"
    max_file_size: int = 100 * 1024 * 1024  # 100MB
    
    # 服务配置
    host: str = "0.0.0.0"
    port: int = 8000
    
    class Config:
        env_file = ".env"
        case_sensitive = False


# 全局配置实例
settings = Settings()


def get_registry_url(is_artifact: bool = False) -> str:
    """根据类型返回对应的registry URL"""
    if is_artifact:
        return settings.registry_artifacts_url
    return settings.registry_library_url


def get_full_image_name(image_name: str, tag: str = "latest", is_artifact: bool = False) -> str:
    """构建完整的镜像名称"""
    registry_url = get_registry_url(is_artifact)
    return f"{registry_url}/{image_name}:{tag}"
