# Docker Pull-Push API Curl 命令

## 基本用法

### 1. 健康检查
```bash
curl -X GET http://localhost:8000/api/health
```

### 2. Pull-Push 镜像
```bash
curl -X POST http://localhost:8000/api/pull-push \
  -H "Content-Type: application/json" \
  -d '{
    "source_image": "nginx:latest",
    "target_image": "my-registry/nginx",
    "tag": "latest"
  }'
```

### 3. 获取配置信息
```bash
curl -X GET http://localhost:8000/api/config
```

## 详细示例

### Pull-Push Redis 镜像
```bash
curl -X POST http://localhost:8000/api/pull-push \
  -H "Content-Type: application/json" \
  -d '{
    "source_image": "redis:6.2",
    "target_image": "my-registry/redis",
    "tag": "6.2"
  }'
```

### Pull-Push MySQL 镜像
```bash
curl -X POST http://localhost:8000/api/pull-push \
  -H "Content-Type: application/json" \
  -d '{
    "source_image": "mysql:8.0",
    "target_image": "my-registry/mysql",
    "tag": "8.0"
  }'
```

### 带错误处理的完整命令
```bash
response=$(curl -s -w "\n%{http_code}" \
  -X POST http://localhost:8000/api/pull-push \
  -H "Content-Type: application/json" \
  -d '{
    "source_image": "nginx:latest",
    "target_image": "my-registry/nginx",
    "tag": "latest"
  }')

http_code=$(echo "$response" | tail -n1)
response_body=$(echo "$response" | head -n -1)

if [ "$http_code" -eq 200 ]; then
    echo "成功: $response_body"
else
    echo "失败 (HTTP $http_code): $response_body"
fi
```

## 一行命令版本

### 简单版本
```bash
curl -X POST http://localhost:8000/api/pull-push -H "Content-Type: application/json" -d '{"source_image":"nginx:latest","target_image":"my-registry/nginx","tag":"latest"}'
```

### 带格式化输出
```bash
curl -X POST http://localhost:8000/api/pull-push -H "Content-Type: application/json" -d '{"source_image":"nginx:latest","target_image":"my-registry/nginx","tag":"latest"}' | python3 -m json.tool
```

## 使用变量的版本

```bash
# 设置变量
SOURCE_IMAGE="nginx:latest"
TARGET_IMAGE="my-registry/nginx"
TAG="latest"
API_HOST="localhost"
API_PORT="8000"

# 执行请求
curl -X POST "http://${API_HOST}:${API_PORT}/api/pull-push" \
  -H "Content-Type: application/json" \
  -d "{
    \"source_image\": \"${SOURCE_IMAGE}\",
    \"target_image\": \"${TARGET_IMAGE}\",
    \"tag\": \"${TAG}\"
  }"
```

## 批量操作示例

```bash
# 定义镜像列表
images=(
  "nginx:latest,my-registry/nginx,latest"
  "redis:6.2,my-registry/redis,6.2"
  "mysql:8.0,my-registry/mysql,8.0"
)

# 批量执行
for image_info in "${images[@]}"; do
  IFS=',' read -r source target tag <<< "$image_info"
  echo "Processing: $source -> $target:$tag"
  
  curl -X POST http://localhost:8000/api/pull-push \
    -H "Content-Type: application/json" \
    -d "{
      \"source_image\": \"$source\",
      \"target_image\": \"$target\",
      \"tag\": \"$tag\"
    }" | python3 -m json.tool
  
  echo "---"
done
```
