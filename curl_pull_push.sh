#!/bin/bash

# Docker Pull-Push API 调用脚本
# 使用curl调用Docker服务的pull-push接口

# 默认配置
DEFAULT_HOST="localhost"
DEFAULT_PORT="8000"
DEFAULT_TAG="latest"

# 显示使用说明
show_usage() {
    echo "使用方法:"
    echo "  $0 <source_image> <target_image> [options]"
    echo ""
    echo "参数:"
    echo "  source_image    源镜像名称 (必需)"
    echo "  target_image    目标镜像名称 (必需)"
    echo ""
    echo "选项:"
    echo "  -h, --host      API服务器地址 (默认: $DEFAULT_HOST)"
    echo "  -p, --port      API服务器端口 (默认: $DEFAULT_PORT)"
    echo "  -t, --tag       镜像标签 (默认: $DEFAULT_TAG)"
    echo "  --help          显示此帮助信息"
    echo ""
    echo "示例:"
    echo "  $0 nginx:latest my-registry/nginx"
    echo "  $0 redis:6.2 my-registry/redis -t 6.2 -h api.example.com -p 9000"
    echo ""
}

# 解析命令行参数
parse_args() {
    if [ $# -lt 2 ]; then
        echo "错误: 缺少必需参数"
        show_usage
        exit 1
    fi

    SOURCE_IMAGE="$1"
    TARGET_IMAGE="$2"
    shift 2

    HOST="$DEFAULT_HOST"
    PORT="$DEFAULT_PORT"
    TAG="$DEFAULT_TAG"

    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--host)
                HOST="$2"
                shift 2
                ;;
            -p|--port)
                PORT="$2"
                shift 2
                ;;
            -t|--tag)
                TAG="$2"
                shift 2
                ;;
            --help)
                show_usage
                exit 0
                ;;
            *)
                echo "错误: 未知选项 $1"
                show_usage
                exit 1
                ;;
        esac
    done
}

# 验证参数
validate_args() {
    if [ -z "$SOURCE_IMAGE" ]; then
        echo "错误: 源镜像名称不能为空"
        exit 1
    fi

    if [ -z "$TARGET_IMAGE" ]; then
        echo "错误: 目标镜像名称不能为空"
        exit 1
    fi

    # 验证端口号
    if ! [[ "$PORT" =~ ^[0-9]+$ ]] || [ "$PORT" -lt 1 ] || [ "$PORT" -gt 65535 ]; then
        echo "错误: 无效的端口号: $PORT"
        exit 1
    fi
}

# 检查API服务是否可用
check_api_health() {
    local url="http://${HOST}:${PORT}/api/health"
    echo "检查API服务状态: $url"
    
    if ! curl -s --connect-timeout 5 "$url" > /dev/null; then
        echo "错误: 无法连接到API服务 ($url)"
        echo "请确保服务正在运行并且地址端口正确"
        exit 1
    fi
    
    echo "✓ API服务连接正常"
}

# 执行pull-push操作
do_pull_push() {
    local url="http://${HOST}:${PORT}/api/pull-push"
    local json_data=$(cat <<EOF
{
    "source_image": "$SOURCE_IMAGE",
    "target_image": "$TARGET_IMAGE",
    "tag": "$TAG"
}
EOF
)

    echo ""
    echo "开始执行pull-push操作..."
    echo "源镜像: $SOURCE_IMAGE"
    echo "目标镜像: $TARGET_IMAGE:$TAG"
    echo "API地址: $url"
    echo ""

    # 执行curl请求
    local response=$(curl -s -w "\n%{http_code}" \
        -X POST \
        -H "Content-Type: application/json" \
        -d "$json_data" \
        "$url")

    # 分离响应体和状态码
    local http_code=$(echo "$response" | tail -n1)
    local response_body=$(echo "$response" | head -n -1)

    echo "HTTP状态码: $http_code"
    echo "响应内容:"
    echo "$response_body" | python3 -m json.tool 2>/dev/null || echo "$response_body"

    # 检查HTTP状态码
    if [ "$http_code" -eq 200 ]; then
        echo ""
        echo "✓ Pull-Push操作成功完成!"
        return 0
    else
        echo ""
        echo "✗ Pull-Push操作失败 (HTTP $http_code)"
        return 1
    fi
}

# 主函数
main() {
    echo "Docker Pull-Push API 客户端"
    echo "=========================="
    
    # 解析参数
    parse_args "$@"
    
    # 验证参数
    validate_args
    
    # 检查API健康状态
    check_api_health
    
    # 执行pull-push
    if do_pull_push; then
        exit 0
    else
        exit 1
    fi
}

# 如果脚本被直接执行（不是被source）
if [ "${BASH_SOURCE[0]}" == "${0}" ]; then
    main "$@"
fi
