services:
  docker-bridge:
    container_name: docker-bridge
    build: .
    ports:
      - "8015:8015"
    volumes:
      - ./temp:/app/temp
    environment:
      - REGISTRY_LIBRARY_URL=registry.datasecchk.net/library
      - REGISTRY_ARTIFACTS_URL=registry.datasecchk.net/artifacts
      - REGISTRY_USERNAME=${REGISTRY_USERNAME}
      - REGISTRY_PASSWORD=${REGISTRY_PASSWORD}
    restart: unless-stopped

