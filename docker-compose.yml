version: '3.8'

services:
  docker-service:
    build: .
    ports:
      - "8000:8000"
    volumes:
      - ./temp:/app/temp
    environment:
      - REGISTRY_LIBRARY_URL=registry.datasecchk.net/library
      - REGISTRY_ARTIFACTS_URL=registry.datasecchk.net/artifacts
      - REGISTRY_USERNAME=${REGISTRY_USERNAME}
      - REGISTRY_PASSWORD=${REGISTRY_PASSWORD}
    restart: unless-stopped
    container_name: docker-service
